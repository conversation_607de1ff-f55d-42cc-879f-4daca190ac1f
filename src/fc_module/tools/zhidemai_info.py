import os
import requests
import hashlib
import time
import json
from urllib.parse import urlencode

# 假设 app_key 和 app_secret 是环境变量
APP_KEY = "z13408f3a0"
# os.getenv('APP_KEY',"z13408f3a0")
print("APP_KEY")
APP_SECRET = "e4973a4589bd44e0201db869cf77279e"
# os.getenv('APP_SECRET',"e4973a4589bd44e0201db869cf77279e")
print("APP_SECRET")

# --- 1. 引导用户授权（这部分通常在前端或通过浏览器完成） ---
# 示例：
# authorize_url = f"https://openapi.zhidemai.com/oauth2/authorize?response_type=code&client_id={APP_KEY}&redirect_uri=YOUR_REDIRECT_URI"
# print(f"请在浏览器中打开此链接进行授权: {authorize_url}")
# 用户授权后，会重定向到 YOUR_REDIRECT_URI?code=YOUR_CODE

# 假设您已经获取到授权码 'code'
# 请替换为实际的 code
AUTH_CODE = "YOUR_RECEIVED_CODE_AFTER_AUTHORIZATION"
REDIRECT_URI = "YOUR_REGISTERED_REDIRECT_URI" # 必须与开发者平台注册的回调地址一致

def generate_authorization_url(redirect_uri, state=None):
    """
    生成OAuth2.0授权URL

    Args:
        redirect_uri (str): 回调地址，必须与开发者平台注册的地址一致
        state (str): 可选的状态参数，用于防止CSRF攻击

    Returns:
        str: 授权URL

    文档: https://openapi.zhidemai.com/pages/rookie/3.OAuth2.0%E8%B0%83%E7%94%A8%E8%AF%A6%E8%A7%A3.html
    """
    if not APP_KEY:
        raise ValueError("APP_KEY must be set.")

    base_url = "https://openapi.zhidemai.com/oauth2/authorize"
    params = {
        "response_type": "code",
        "client_id": APP_KEY,
        "redirect_uri": redirect_uri
    }

    if state:
        params["state"] = state

    query_string = urlencode(params)
    authorization_url = f"{base_url}?{query_string}"

    return authorization_url


def get_access_token_by_code(code, app_secret=None):
    """
    根据授权码获取access_token - 根据官方文档更新
    文档: https://openapi.zhidemai.com/pages/oauth2/1.%E6%A0%B9%E6%8D%AEcode%E8%8E%B7%E5%8F%96access_token.html

    Args:
        code (str): 用户授权后获取到的code参数，必填
        app_secret (str): 应用密钥，可选，默认使用全局APP_SECRET

    Returns:
        dict: API响应数据，包含以下结构：
            - error_code: 错误码，0表示正常
            - error_msg: 错误信息
            - data: 数据节点信息
                - union_id: 用户统一标识
                - access_token: 接口调用凭证
                - expires_in: 超时时间（秒）

    Raises:
        ValueError: 当参数不合法时抛出异常
    """
    # 参数验证
    if not code:
        raise ValueError("code 参数不能为空")

    if not APP_KEY:
        raise ValueError("APP_KEY 不能为空")

    secret = app_secret or APP_SECRET
    if not secret:
        raise ValueError("app_secret 不能为空")

    # API接口地址 - 根据文档更新
    url = "https://openapi.zhidemai.com/v1/oauth/check/code"

    # 请求参数 - 根据文档规范
    params = {
        "code": code,
        "app_secret": secret
    }

    print(f"\n正在获取access_token (code={code[:10]}...)...")

    try:
        # 发起POST请求
        response = requests.post(url, data=params)
        response.raise_for_status()  # 检查HTTP响应状态

        result = response.json()

        if result:
            error_code = result.get("error_code", "")
            if error_code == "0":
                print("✓ access_token获取成功")
                data = result.get("data", {})
                access_token = data.get("access_token", "")
                expires_in = data.get("expires_in", 0)
                union_id = data.get("union_id", "")

                print(f"  access_token: {access_token[:20]}...")
                print(f"  expires_in: {expires_in}秒")
                print(f"  union_id: {union_id}")

                return result
            else:
                error_msg = result.get("error_msg", "未知错误")
                print(f"✗ 获取access_token失败: {error_code} - {error_msg}")
                return result
        else:
            print("✗ 获取access_token失败: 响应为空")
            return None

    except requests.exceptions.RequestException as e:
        print(f"✗ 请求过程中发生异常: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"✗ JSON解析失败: {e}")
        return None


def get_access_token(code, redirect_uri):
    """
    兼容性函数 - 保持向后兼容
    推荐使用 get_access_token_by_code 函数

    Args:
        code (str): 授权码
        redirect_uri (str): 回调地址（此参数在新API中不需要，保留用于兼容性）
    """
    print("⚠️  使用兼容性函数，推荐使用 get_access_token_by_code")
    print(f"注意: redirect_uri参数({redirect_uri})在新API中不需要")
    return get_access_token_by_code(code)


def parse_access_token_response(response_data):
    """
    解析access_token响应数据

    Args:
        response_data (dict): API响应数据

    Returns:
        dict: 解析后的token信息，失败时返回None
    """
    if not response_data or response_data.get("error_code") != "0":
        return None

    data = response_data.get("data", {})

    return {
        "access_token": data.get("access_token", ""),
        "expires_in": data.get("expires_in", 0),
        "union_id": data.get("union_id", ""),
        "expires_at": int(time.time()) + data.get("expires_in", 0)  # 计算过期时间戳
    }


def is_access_token_valid(token_info):
    """
    检查access_token是否仍然有效

    Args:
        token_info (dict): 包含expires_at字段的token信息

    Returns:
        bool: True表示有效，False表示已过期
    """
    if not token_info or not token_info.get("expires_at"):
        return False

    current_time = int(time.time())
    expires_at = token_info.get("expires_at", 0)

    # 提前5分钟判断为过期，避免边界情况
    buffer_time = 300  # 5分钟

    return current_time < (expires_at - buffer_time)


def oauth2_complete_flow(redirect_uri, state=None):
    """
    完整的OAuth2.0授权流程演示

    Args:
        redirect_uri (str): 回调地址
        state (str): 可选的状态参数

    Returns:
        dict: 包含授权URL和使用说明
    """
    print("🔐 OAuth2.0完整授权流程")
    print("=" * 50)

    # 步骤1: 生成授权URL
    auth_url = generate_authorization_url(redirect_uri, state)

    print("📋 授权流程步骤:")
    print("1️⃣ 用户访问授权URL")
    print("2️⃣ 用户登录并授权")
    print("3️⃣ 系统重定向到回调地址，携带code参数")
    print("4️⃣ 使用code换取access_token")

    print(f"\n🔗 授权URL:")
    print(f"{auth_url}")

    print(f"\n📝 回调地址示例:")
    print(f"{redirect_uri}?code=AUTHORIZATION_CODE&state={state or 'N/A'}")

    print(f"\n💻 获取token的代码示例:")
    print("```python")
    print("# 从回调URL中提取code参数")
    print("code = 'AUTHORIZATION_CODE_FROM_CALLBACK'")
    print("")
    print("# 获取access_token")
    print("token_response = get_access_token_by_code(code)")
    print("if token_response and token_response.get('error_code') == '0':")
    print("    token_info = parse_access_token_response(token_response)")
    print("    access_token = token_info['access_token']")
    print("    print(f'获取到access_token: {access_token}')")
    print("```")

    return {
        "authorization_url": auth_url,
        "redirect_uri": redirect_uri,
        "state": state,
        "instructions": "请在浏览器中打开授权URL，完成授权后从回调地址获取code参数"
    }

# --- 调用示例 ---
# access_token_info = get_access_token(AUTH_CODE, REDIRECT_URI)
# if access_token_info and access_token_info.get("access_token"):
#     ACCESS_TOKEN = access_token_info["access_token"]
#     print(f"成功获取 access_token: {ACCESS_TOKEN}")
# else:
#     print("未能获取到 access_token，请检查配置或授权流程。")
#     ACCESS_TOKEN = None # 如果获取失败，设置为None

def generate_sign(params, app_secret):
    """
    生成签名
    文档: https://openapi.zhidemai.com/pages/rookie/2.API%E8%B0%83%E7%94%A8%E6%96%B9%E6%B3%95%E8%AF%A6%E8%A7%A3.html
    """
    # 1. 对所有请求参数按参数名进行字典升序排序
    sorted_params = sorted(params.items())

    # 2. 拼接参数名和参数值
    string_to_sign = app_secret + "".join([f"{k}{v}" for k, v in sorted_params]) + app_secret

    # 3. 使用 MD5 对字符串进行加密，并转换成大写
    sign = hashlib.md5(string_to_sign.encode('utf-8')).hexdigest().upper()
    return sign

def make_api_request(method, url, params, access_token):
    """
    发起 API 请求的通用函数
    """
    if not APP_KEY or not APP_SECRET:
        raise ValueError("APP_KEY and APP_SECRET environment variables must be set.")
    if not access_token:
        raise ValueError("access_token is required for API requests.")

    # 公共参数
    common_params = {
        "app_key": APP_KEY,
        "timestamp": str(int(time.time() * 1000)), # 毫秒级时间戳
        "v": "1.0", # API版本
        "format": "json",
        "access_token": access_token
    }

    # 合并业务参数和公共参数
    all_params = {**params, **common_params}

    # 生成签名
    sign = generate_sign(all_params, APP_SECRET)
    all_params["sign"] = sign

    try:
        if method.upper() == 'GET':
            response = requests.get(url, params=all_params)
        elif method.upper() == 'POST':
            response = requests.post(url, data=all_params)
        else:
            raise ValueError("Unsupported HTTP method")

        response.raise_for_status() # 检查HTTP响应状态
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"API 请求失败: {e}")
        return None

# 为了演示，假设我们已经有一个有效的 ACCESS_TOKEN
# 在实际应用中，您需要先运行 get_access_token 获取它
# ！！！！请替换为您的实际 ACCESS_TOKEN！！！！
# 例如：
# ACCESS_TOKEN = "YOUR_ACTUAL_ACCESS_TOKEN_HERE"
# 或者如果上面成功获取，就用 access_token_info["access_token"]
ACCESS_TOKEN = "YOUR_MOCKED_ACCESS_TOKEN_FOR_DEMO" # 临时值，请替换为真实获取的token
if not ACCESS_TOKEN or ACCESS_TOKEN == "YOUR_MOCKED_ACCESS_TOKEN_FOR_DEMO":
    print("\n警告: 请替换 ACCESS_TOKEN 为真实值以运行后续接口调用示例。")


def test_api_calling_method():
    """
    测试API调用方法详解 - 根据官方文档实现的测试函数
    文档: https://openapi.zhidemai.com/pages/rookie/2.API%E8%B0%83%E7%94%A8%E6%96%B9%E6%B3%95%E8%AF%A6%E8%A7%A3.html

    此函数演示了完整的API调用流程：
    1. 准备公共参数和业务参数
    2. 按ASCII升序排序参数
    3. 生成签名
    4. 发起HTTP请求
    """
    print("=== Zhidemai API调用方法测试 ===\n")

    # 1. 准备测试参数（模拟官方文档示例）
    print("1. 准备测试参数:")
    test_params = {
        'timestamp': str(int(time.time())),  # 使用当前时间戳
        'app_key': APP_KEY,
        'order_status': '1',
        'page_size': '10',
        'page': '1',
        'v': '1.0',
        'format': 'json'
    }

    print("原始参数:")
    for key, value in test_params.items():
        print(f"  {key}: {value}")

    # 2. 按首字母升序排列（ASCII ASC）
    print("\n2. 按ASCII升序排序参数:")
    sorted_params = sorted(test_params.items())
    print("排序后参数:")
    for key, value in sorted_params:
        print(f"  {key}: {value}")

    # 3. 拼接字符串
    print("\n3. 拼接签名字符串:")
    param_string = "".join([f"{k}{v}" for k, v in sorted_params])
    sign_string = APP_SECRET + param_string + APP_SECRET
    print(f"参数拼接: {param_string}")
    print(f"完整签名字符串: {sign_string}")

    # 4. 生成签名
    print("\n4. 生成MD5签名:")
    sign = hashlib.md5(sign_string.encode('utf-8')).hexdigest().upper()
    print(f"MD5签名: {sign}")

    # 5. 组装最终请求参数
    print("\n5. 组装最终请求参数:")
    final_params = test_params.copy()
    final_params['sign'] = sign

    print("最终请求参数:")
    for key, value in final_params.items():
        print(f"  {key}: {value}")

    # 6. 构建请求URL（演示用）
    print("\n6. 构建请求URL:")
    base_url = "https://openapi.zhidemai.com/api/test"
    query_string = urlencode(final_params)
    full_url = f"{base_url}?{query_string}"
    print(f"完整URL: {full_url}")

    # 7. 验证签名算法（使用内置函数）
    print("\n7. 验证签名算法:")
    verified_sign = generate_sign(test_params, APP_SECRET)
    print(f"内置函数生成签名: {verified_sign}")
    print(f"签名验证: {'✓ 通过' if sign == verified_sign else '✗ 失败'}")

    return {
        'original_params': test_params,
        'sorted_params': sorted_params,
        'sign_string': sign_string,
        'signature': sign,
        'final_params': final_params,
        'full_url': full_url,
        'verification_passed': sign == verified_sign
    }


def test_real_api_call_with_mock_token():
    """
    使用模拟token测试真实API调用
    注意: 需要有效的access_token才能成功调用
    """
    print("\n=== 真实API调用测试 ===\n")

    # 测试参数
    test_params = {
        "page": "1",
        "page_size": "5",
        "method": "api.haojia.articles.list"
    }

    print("测试参数:")
    for key, value in test_params.items():
        print(f"  {key}: {value}")

    # 如果有真实的access_token，可以进行真实调用
    if ACCESS_TOKEN and ACCESS_TOKEN != "YOUR_MOCKED_ACCESS_TOKEN_FOR_DEMO":
        print("\n发起真实API调用...")
        url = "https://openapi.zhidemai.com/api/haojia/articles/list"
        result = make_api_request("GET", url, test_params, ACCESS_TOKEN)

        if result:
            print("✓ API调用成功")
            print(f"返回数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print("✗ API调用失败")
    else:
        print("\n⚠️  需要有效的access_token才能进行真实API调用")
        print("请先获取access_token或设置有效的ACCESS_TOKEN变量")

        # 演示如何构建请求
        print("\n演示请求构建过程:")
        mock_token = "mock_access_token_for_demo"

        # 公共参数
        common_params = {
            "app_key": APP_KEY,
            "timestamp": str(int(time.time() * 1000)),
            "v": "1.0",
            "format": "json",
            "access_token": mock_token
        }

        # 合并参数
        all_params = {**test_params, **common_params}

        # 生成签名
        sign = generate_sign(all_params, APP_SECRET)
        all_params["sign"] = sign

        print("完整请求参数:")
        for key, value in all_params.items():
            print(f"  {key}: {value}")

        # 构建URL
        url = "https://openapi.zhidemai.com/api/haojia/articles/list"
        query_string = urlencode(all_params)
        full_url = f"{url}?{query_string}"
        print(f"\n完整请求URL:\n{full_url}")


def run_all_tests():
    """
    运行所有测试函数
    """
    print("开始运行Zhidemai API调用方法测试...\n")

    # 测试1: API调用方法详解
    test_result = test_api_calling_method()

    # 测试2: 真实API调用测试
    test_real_api_call_with_mock_token()

    # 测试3: 好价信息详情接口测试
    test_haojia_detail_api()

    print("\n=== 测试总结 ===")
    print(f"APP_KEY: {APP_KEY}")
    print(f"APP_SECRET: {APP_SECRET[:8]}...")  # 只显示前8位
    print(f"签名验证: {'✓ 通过' if test_result['verification_passed'] else '✗ 失败'}")
    print("✓ 好价信息详情接口封装完成")
    print("\n测试完成！")

    return test_result


def get_haojia_detail_simple(article_id, access_token=None):
    """
    简化版好价信息详情获取函数
    自动使用版本2并解析返回结构化数据

    Args:
        article_id (str): 好价文章ID
        access_token (str): 访问令牌，可选

    Returns:
        dict: 解析后的结构化数据，失败时返回None
    """
    try:
        # 获取原始数据
        raw_data = get_haojia_info_detail(article_id, version="2", access_token=access_token)

        if not raw_data:
            return None

        # 解析数据
        parsed_data = parse_haojia_detail_v2(raw_data)
        return parsed_data

    except Exception as e:
        print(f"获取好价详情失败: {e}")
        return None


def print_haojia_detail_summary(parsed_data):
    """
    打印好价信息详情摘要

    Args:
        parsed_data (dict): 解析后的好价详情数据
    """
    if not parsed_data:
        print("❌ 无有效数据")
        return

    print("\n" + "="*60)
    print("📋 好价信息详情摘要")
    print("="*60)

    # 基本信息
    print(f"📰 标题: {parsed_data.get('title', 'N/A')}")
    print(f"💰 价格: {parsed_data.get('subtitle', 'N/A')}")
    print(f"🏪 商城: {parsed_data.get('mall', 'N/A')}")
    print(f"🏷️  分类: {parsed_data.get('category', 'N/A')}")
    print(f"📅 发布时间: {parsed_data.get('publish_date', 'N/A')}")

    # 价格信息
    price_info = parsed_data.get('price', {})
    if price_info:
        print(f"💵 页面价: {price_info.get('page_price', 'N/A')} {price_info.get('coin_unit', '')}")
        print(f"💸 折后价: {price_info.get('digital_price', 'N/A')} {price_info.get('coin_unit', '')}")

    # 用户信息
    user_info = parsed_data.get('user', {})
    if user_info.get('nickname'):
        print(f"👤 爆料人: {user_info.get('nickname', 'N/A')}")

    # 统计信息
    stats = parsed_data.get('stats', {})
    print(f"📊 统计: 👍{stats.get('worthy', 0)} 👎{stats.get('unworthy', 0)} ❤️{stats.get('collection', 0)} 💬{stats.get('comment', 0)}")

    # 状态信息
    status = parsed_data.get('status', {})
    if status.get('is_sold_out'):
        print("⚠️  状态: 已售罄")
    elif status.get('is_timeout'):
        print("⚠️  状态: 已过期")
    else:
        print("✅ 状态: 正常")

    # 优惠券信息
    coupons = parsed_data.get('coupons', [])
    if coupons:
        print(f"🎫 优惠券: {len(coupons)}张")
        for i, coupon in enumerate(coupons[:3], 1):  # 只显示前3张
            print(f"   {i}. {coupon.get('title', 'N/A')}")

    # 值法标签
    zhifa_tags = parsed_data.get('zhifa_tags', [])
    if zhifa_tags:
        print(f"🏷️  值法标签: {', '.join(zhifa_tags)}")

    # 链接信息
    print(f"🔗 商品链接: {parsed_data.get('product', {}).get('clean_link', 'N/A')}")
    print(f"📱 详情页面: {parsed_data.get('url', 'N/A')}")

    print("="*60)


def test_haojia_detail_api():
    """
    测试好价信息详情接口
    """
    print("\n=== 好价信息详情接口测试 ===\n")

    # 测试文章ID（可以从好价文章列表中获取）
    test_article_ids = [
        "13284568",  # 示例ID
        "21513355",  # 文档中的示例ID
    ]

    for article_id in test_article_ids:
        print(f"\n🔍 测试文章ID: {article_id}")

        if ACCESS_TOKEN and ACCESS_TOKEN != "YOUR_MOCKED_ACCESS_TOKEN_FOR_DEMO":
            # 测试版本1
            print("\n--- 测试版本1 ---")
            v1_data = get_haojia_info_detail(article_id, version="1")
            if v1_data:
                parsed_v1 = parse_haojia_detail_v1(v1_data)
                if parsed_v1:
                    print(f"✓ 版本1数据获取成功")
                    print(f"  标题: {parsed_v1.get('title', 'N/A')}")
                    print(f"  购买链接: {parsed_v1.get('buy_url', 'N/A')}")
                else:
                    print("✗ 版本1数据解析失败")

            # 测试版本2
            print("\n--- 测试版本2 ---")
            v2_data = get_haojia_info_detail(article_id, version="2")
            if v2_data:
                parsed_v2 = parse_haojia_detail_v2(v2_data)
                if parsed_v2:
                    print(f"✓ 版本2数据获取成功")
                    print_haojia_detail_summary(parsed_v2)
                else:
                    print("✗ 版本2数据解析失败")

            # 测试简化版函数
            print("\n--- 测试简化版函数 ---")
            simple_data = get_haojia_detail_simple(article_id)
            if simple_data:
                print("✓ 简化版函数调用成功")
            else:
                print("✗ 简化版函数调用失败")

        else:
            print("⚠️  需要有效的access_token才能进行真实API调用")
            print("演示参数构建过程:")

            # 演示参数构建
            demo_params = {
                "article_id": article_id,
                "version": "2"
            }

            print("请求参数:")
            for key, value in demo_params.items():
                print(f"  {key}: {value}")

            print(f"请求URL: https://openapi.zhidemai.com/v1/youhui/detail/show")

        print("-" * 50)


def test_oauth2_flow():
    """
    测试OAuth2.0授权流程
    """
    print("\n=== OAuth2.0授权流程测试 ===\n")

    # 测试授权URL生成
    print("1️⃣ 测试授权URL生成:")
    test_redirect_uri = "https://your-app.com/callback"
    test_state = "random_state_string_123"

    try:
        auth_url = generate_authorization_url(test_redirect_uri, test_state)
        print(f"✓ 授权URL生成成功")
        print(f"  URL: {auth_url}")

        # 验证URL包含必要参数
        if APP_KEY in auth_url and test_redirect_uri in auth_url:
            print("✓ URL参数验证通过")
        else:
            print("✗ URL参数验证失败")

    except Exception as e:
        print(f"✗ 授权URL生成失败: {e}")

    # 测试access_token获取（模拟）
    print("\n2️⃣ 测试access_token获取:")
    test_code = "mock_authorization_code_123"

    print(f"模拟授权码: {test_code}")
    print("⚠️  注意: 需要真实的授权码才能成功获取access_token")

    # 演示参数构建
    print("\n请求参数演示:")
    demo_params = {
        "code": test_code,
        "app_secret": APP_SECRET
    }

    for key, value in demo_params.items():
        if key == "app_secret":
            print(f"  {key}: {value[:8]}...")
        else:
            print(f"  {key}: {value}")

    print(f"请求URL: https://openapi.zhidemai.com/v1/oauth/check/code")

    # 如果有真实的授权码，可以尝试获取
    real_code = input("\n输入真实的授权码进行测试 (直接回车跳过): ").strip()
    if real_code:
        print(f"\n🔄 使用真实授权码测试: {real_code[:10]}...")
        token_response = get_access_token_by_code(real_code)

        if token_response:
            if token_response.get("error_code") == "0":
                print("✓ access_token获取成功!")
                token_info = parse_access_token_response(token_response)
                if token_info:
                    print(f"  access_token: {token_info['access_token'][:20]}...")
                    print(f"  expires_in: {token_info['expires_in']}秒")
                    print(f"  union_id: {token_info['union_id']}")
                    print(f"  有效性检查: {'✓ 有效' if is_access_token_valid(token_info) else '✗ 已过期'}")
            else:
                error_msg = token_response.get("error_msg", "未知错误")
                print(f"✗ access_token获取失败: {error_msg}")
        else:
            print("✗ 请求失败")
    else:
        print("⏭️  跳过真实授权码测试")

    # 测试完整流程演示
    print("\n3️⃣ 完整OAuth2.0流程演示:")
    flow_info = oauth2_complete_flow(test_redirect_uri, test_state)

    print(f"\n📋 流程信息:")
    print(f"  授权URL: {flow_info['authorization_url'][:50]}...")
    print(f"  回调地址: {flow_info['redirect_uri']}")
    print(f"  状态参数: {flow_info['state']}")
    print(f"  说明: {flow_info['instructions']}")


def test_token_management():
    """
    测试token管理功能
    """
    print("\n=== Token管理功能测试 ===\n")

    # 模拟token信息
    mock_token_info = {
        "access_token": "mock_token_12345678901234567890",
        "expires_in": 7200,
        "union_id": "mock_union_id_123",
        "expires_at": int(time.time()) + 7200
    }

    print("1️⃣ 测试token信息解析:")
    print(f"  access_token: {mock_token_info['access_token'][:20]}...")
    print(f"  expires_in: {mock_token_info['expires_in']}秒")
    print(f"  union_id: {mock_token_info['union_id']}")
    print(f"  expires_at: {mock_token_info['expires_at']}")

    print("\n2️⃣ 测试token有效性检查:")
    is_valid = is_access_token_valid(mock_token_info)
    print(f"  当前时间: {int(time.time())}")
    print(f"  过期时间: {mock_token_info['expires_at']}")
    print(f"  有效性: {'✓ 有效' if is_valid else '✗ 已过期'}")

    # 测试过期token
    print("\n3️⃣ 测试过期token:")
    expired_token_info = mock_token_info.copy()
    expired_token_info['expires_at'] = int(time.time()) - 3600  # 1小时前过期

    is_expired = is_access_token_valid(expired_token_info)
    print(f"  过期token有效性: {'✓ 有效' if is_expired else '✗ 已过期'}")

    print("\n4️⃣ Token管理最佳实践:")
    print("  - 在每次API调用前检查token有效性")
    print("  - 提前5分钟刷新token避免边界情况")
    print("  - 安全存储access_token和相关信息")
    print("  - 记录union_id用于用户身份识别")


def get_haojia_article_list(page=1, page_size=20, category_id=None, tag_id=None, sort_type="0"):
    """
    获取好价文章列表
    """
    url = "https://openapi.zhidemai.com/api/haojia/articles/list"
    params = {
        "page": str(page),
        "page_size": str(page_size),
        "method": "api.haojia.articles.list" # 业务方法名
    }
    if category_id:
        params["category_id"] = str(category_id)
    if tag_id:
        params["tag_id"] = str(tag_id)
    params["sort_type"] = str(sort_type) # 排序类型，0-默认，1-热门

    print(f"\n正在请求好价文章列表 (page={page}, page_size={page_size})...")
    return make_api_request("GET", url, params, ACCESS_TOKEN)

# --- 调用示例 ---
if ACCESS_TOKEN and ACCESS_TOKEN != "YOUR_MOCKED_ACCESS_TOKEN_FOR_DEMO":
    article_list_data = get_haojia_article_list(page=1, page_size=10)
    if article_list_data:
        print("好价文章列表获取成功:")
        # print(json.dumps(article_list_data, indent=2, ensure_ascii=False))
        for item in article_list_data.get("data", {}).get("list", []):
            print(f"  文章ID: {item.get('article_id')}, 标题: {item.get('article_title')}, 价格: {item.get('article_price')}")
        print(f"总条数: {article_list_data.get('data', {}).get('total_count')}")
    else:
        print("获取好价文章列表失败。")



def get_haojia_article_detail(article_id):
    """
    获取好价文章详情
    """
    url = "https://openapi.zhidemai.com/api/haojia/articles/detail"
    params = {
        "article_id": str(article_id),
        "method": "api.haojia.articles.detail" # 业务方法名
    }

    print(f"\n正在请求好价文章详情 (article_id={article_id})...")
    return make_api_request("GET", url, params, ACCESS_TOKEN)


def get_haojia_info_detail(article_id, version="2", access_token=None):
    """
    好价信息详情接口 - 根据官方文档封装
    文档: https://openapi.zhidemai.com/pages/haojia/1.%E5%A5%BD%E4%BB%B7%E4%BF%A1%E6%81%AF%E8%AF%A6%E6%83%85%E6%8E%A5%E5%8F%A3.html

    Args:
        article_id (str): 好价文章ID，必填
        version (str): 版本号，支持 "1" 或 "2"，默认为 "2"
                      - version 1: 返回基础信息（标题、内容、购买链接等）
                      - version 2: 返回详细信息（包含用户信息、优惠券、标签等）
        access_token (str): 访问令牌，如果不提供则使用全局ACCESS_TOKEN

    Returns:
        dict: API响应数据，包含以下结构：
            - error_code: 错误码，0表示正常
            - error_msg: 错误信息
            - data: 数据节点信息

    Raises:
        ValueError: 当参数不合法时抛出异常
    """
    # 参数验证
    if not article_id:
        raise ValueError("article_id 参数不能为空")

    if version not in ["1", "2"]:
        raise ValueError("version 参数必须为 '1' 或 '2'")

    # 使用提供的access_token或全局ACCESS_TOKEN
    token = access_token or ACCESS_TOKEN
    if not token:
        raise ValueError("access_token 不能为空，请提供有效的访问令牌")

    # API接口地址
    url = "https://openapi.zhidemai.com/v1/youhui/detail/show"

    # 请求参数
    params = {
        "article_id": str(article_id),
        "version": str(version)
    }

    print(f"\n正在请求好价信息详情 (article_id={article_id}, version={version})...")

    try:
        # 发起API请求
        response = make_api_request("GET", url, params, token)

        if response:
            error_code = response.get("error_code", "")
            if error_code == "0":
                print(f"✓ 好价信息详情获取成功 (article_id={article_id})")
                return response
            else:
                error_msg = response.get("error_msg", "未知错误")
                print(f"✗ API返回错误: {error_code} - {error_msg}")
                return response
        else:
            print(f"✗ 好价信息详情获取失败 (article_id={article_id})")
            return None

    except Exception as e:
        print(f"✗ 请求过程中发生异常: {e}")
        return None


def parse_haojia_detail_v1(response_data):
    """
    解析版本1的好价信息详情响应数据

    Args:
        response_data (dict): API响应数据

    Returns:
        dict: 解析后的数据结构
    """
    if not response_data or response_data.get("error_code") != "0":
        return None

    data = response_data.get("data", {})

    return {
        "title": data.get("post_title", ""),
        "content": data.get("post_content", ""),
        "buy_url": data.get("post_buy_url", ""),
        "detail_url": data.get("post_url", ""),
        "image": {
            "width": data.get("post_img", {}).get("width", ""),
            "height": data.get("post_img", {}).get("height", "")
        }
    }


def parse_haojia_detail_v2(response_data):
    """
    解析版本2的好价信息详情响应数据

    Args:
        response_data (dict): API响应数据

    Returns:
        dict: 解析后的详细数据结构
    """
    if not response_data or response_data.get("error_code") != "0":
        return None

    data = response_data.get("data", {})

    # 解析用户信息
    user_info = data.get("user", {})

    # 解析优惠券信息
    coupons = []
    for coupon in data.get("coupon", []):
        coupons.append({
            "title": coupon.get("title", ""),
            "url": coupon.get("url", ""),
            "desc": coupon.get("desc", "")
        })

    # 解析凑单加购信息
    add_buy_items = []
    for item in data.get("add_buy_list", []):
        add_buy_items.append({
            "title": item.get("title", ""),
            "url": item.get("url", "")
        })

    # 解析特殊标签
    special_tags = []
    for tag in data.get("special_tag", []):
        special_tags.append({
            "name": tag.get("name", ""),
            "tag_type": tag.get("tag_type", 0)
        })

    # 解析值法标签
    zhifa_tags = [tag.get("name", "") for tag in data.get("zhifa_tag", [])]

    return {
        "article_id": data.get("article_id", ""),
        "title": data.get("article_title", ""),
        "subtitle": data.get("article_subtitle", ""),
        "url": data.get("article_url", ""),
        "wap_url": data.get("article_wap_url", ""),
        "category": data.get("article_top_category", ""),
        "image": data.get("article_img", ""),
        "mall": data.get("article_mall", ""),
        "publish_date": data.get("article_pubdate", ""),
        "price": {
            "page_price": data.get("page_price", ""),
            "digital_price": data.get("digital_price", ""),
            "coin_unit": data.get("coin_unit", "")
        },
        "product": {
            "clean_link": data.get("clean_link", ""),
            "brand": data.get("brand", ""),
            "selling_point": data.get("selling_point", "")
        },
        "user": {
            "nickname": user_info.get("nickname", ""),
            "avatar": user_info.get("avatar", "")
        },
        "content": {
            "bl_reason": data.get("article_bl_reason", ""),
            "content": data.get("article_content", "")
        },
        "stats": {
            "collection": data.get("article_collection", 0),
            "worthy": data.get("article_worthy", 0),
            "unworthy": data.get("article_unworthy", 0),
            "comment": data.get("article_comment", 0)
        },
        "status": {
            "is_sold_out": data.get("article_is_sold_out", 0) == 1,
            "is_timeout": data.get("article_is_timeout", 0) == 1,
            "check_tips": data.get("check_tips", "")
        },
        "coupons": coupons,
        "add_buy_list": add_buy_items,
        "special_tags": special_tags,
        "zhifa_tags": zhifa_tags,
        "article_type": data.get("article_type", ""),
        "hashcode": data.get("hashcode", ""),
        "order_pic": data.get("order_pic", [])
    }

# --- 调用示例 ---
if ACCESS_TOKEN and ACCESS_TOKEN != "YOUR_MOCKED_ACCESS_TOKEN_FOR_DEMO":
    # 假设我们从上面的列表中获取一个文章ID来查询详情
    # 或者手动指定一个已知的文章ID
    example_article_id = "13284568" # 替换为您想查询的实际文章ID

    article_detail_data = get_haojia_article_detail(example_article_id)
    if article_detail_data:
        print("好价文章详情获取成功:")
        # print(json.dumps(article_detail_data, indent=2, ensure_ascii=False))
        detail = article_detail_data.get("data", {})
        print(f"  标题: {detail.get('article_title')}")
        print(f"  价格: {detail.get('article_price')}")
        print(f"  商品链接: {detail.get('article_url')}")
        print(f"  描述: {detail.get('article_content_text')[:100]}...") # 截取部分描述
    else:
        print("获取好价文章详情失败。")


# === 测试函数调用示例 ===
if __name__ == "__main__":
    print("Zhidemai API 测试程序")
    print("=" * 50)

    # 运行所有测试
    run_all_tests()