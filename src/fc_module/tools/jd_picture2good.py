import json
import requests
import uuid


"""
把接口返回的 data 字段转换成正常的中文商品列表。

示例输入（截取）：
response = {
    'status': True,
    'code': 0,
    'message': 'success',
    'data': '[{"name":"& 乐\\u200b\\u200b\\u200b缔\\u200b\\u200b\\u200b1\\u200b\\u200b\\u200b8\\u200b\\u200b\\u200b0\\u200b\\u200b\\u200b件\\u200b\\u200b\\u200b套\\u200b\\u200b\\u200b木\\u200b\\u200b\\u200b盒\\u200b\\u200b\\u200b画\\u200b\\u200b\\u200b笔\\u200b\\u200b\\u200b套\\u200b\\u200b\\u200b装\\u200b\\u200b\\u200b","price":"¥62.6","price_plus":"已补¥7.3"}, ...]',
    'context': 'uid=1956342083107160064&conversation_id=1111111&...'
}
"""

import json
import re

def clean_zero_width(text: str) -> str:
    """删除所有零宽空格（U+200B）以及多余的空格"""
    return text.replace("\u200b", "").strip()

def parse_response(response: dict) -> list:
    """
    把 `response['data']` 解析成普通的列表，每个元素都是
    {'name': '商品名称', 'price': '¥xx.xx', 'price_plus': '已补¥x.xx' (可选)}
    """
    # 第一步：取出 data 字段，它本身是一个 JSON 串
    raw_data = response.get("data")
    if not raw_data:
        return []

    # 有时候后端会把列表包装成字符串，还会出现 {}、[]、空对象等，需要先确保是合法 JSON
    # 若字符串两端多了单引号或多余的转义，需要先对它做一次 json.loads
    try:
        items = json.loads(raw_data)
    except json.JSONDecodeError as e:
        # 如果直接解析失败，尝试先去掉外层的引号再解析
        # 例如: "'[{\"a\":1}]'" -> "[{\"a\":1}]"
        cleaned = raw_data.strip("'\"")
        items = json.loads(cleaned)

    # 第二步：遍历每个商品，清理名称中的零宽空格
    cleaned_items = []
    for item in items:
        # 有可能出现空 dict {}，直接跳过
        if not isinstance(item, dict) or not item:
            continue

        name = clean_zero_width(item.get("name", ""))
        price = item.get("price", "")
        price_plus = item.get("price_plus", "")

        # 可选：把价格前面的 "¥" 去掉，只保留数字（如果你想保留请注释掉下面两行）
        # price = price.lstrip("¥")
        # price_plus = price_plus.lstrip("已补¥")

        cleaned_items.append({
            "name": name,
            "price": price,
            "price_plus": price_plus,
        })

    return cleaned_items



def jd_picture2good(
    scriptParams,
    subInteractiveName,
    req_id="717fb680-b3dc-42a8-a15a-fc8926984d61",
    uid="1956342083107160064",
    scriptId="1000012",
    conversation_id="1111111"
):
    """
    将图片转换为商品信息的函数
    
    Args:
        scriptParams (dict): 脚本参数，包含图片URL等信息，必填
        subInteractiveName (str): 子交互名称，必填
        req_id (str): 请求ID，默认值为"717fb680-b3dc-42a8-a15a-fc8926984d61"
        uid (str): 用户ID，默认值为"1956342083107160064"
        scriptId (str): 脚本ID，默认值为"1000012"
        
    Returns:
        dict: API响应结果
    """
    # ------------------------------------------------------------
    # 1️⃣ 端点 URL
    # ------------------------------------------------------------
    url = "http://test-api-internal.guangfan-ai.online/agent-api/agent_interactive/run"

    # ------------------------------------------------------------
    # 2️⃣ 需要的请求头
    # ------------------------------------------------------------
    headers = {
        "appkey": "UMhmghBLK77BJyf5",
        "Authorization": (
            "eyJhbGciOiJIUzM4NCJ9."
            "eyJ1aWQiOjE5NTYzNDIwODMxMDcxNjAwNjQsImF1aCI6IlJPTEVfR0VFSyIs"
            "ImRpZCI6IjUwRjIxNEM1LTk2NEUtNDEzMC04NjUyLTJEOUZFMkRBNzM4MSIs"
            "ImV4cCI6MTc1OTE0NjYwMH0."
            "B6CAOXomfqpA_KLwHyBc-83sRaXbJrDVAhnNrbbuy46aLkLpkL4_0G1eNUrFsLru"
        ),
        "Content-Type": "application/json",
    }

    # ------------------------------------------------------------
    # 3️⃣ 请求体（payload）
    # ------------------------------------------------------------
    if not conversation_id:
        conversation_id = str(uuid.uuid4()) 

    payload = {
        "context": json.dumps({
            "req_id": req_id,
            "uid": uid,
            "intent_id": "10002",
            "conversation_id": conversation_id,
            "coords": "1, 1",
            "app_id": "com.jingdong.app.mall"
        }, ensure_ascii=False),
        "scriptId": scriptId,
        "subInteractiveName": subInteractiveName,
        "scriptParams": json.dumps(scriptParams, ensure_ascii=False)
    }

    # ------------------------------------------------------------
    # 4️⃣ 发送请求
    # ------------------------------------------------------------
    try:
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        response.raise_for_status()                # 若返回非 2xx 会抛出异常
        data = response.json()                     # 解析返回的 JSON
        return data
    except requests.exceptions.RequestException as err:
        print("⚠️ 请求失败：", err)
        return {"error": str(err)}

def jd_get_product_url(
    imageUrl,
    req_id="717fb680-b3dc-42a8-a15a-fc8926984d61",
    uid="1956342083107160064",
    scriptId="1000012"
):
    conv_id = str(uuid.uuid4())
    uuidNew = str(uuid.uuid4()) +".jpg"# 生成一个随机的 UUID
    scriptParams = {
        "imageUrl": imageUrl,
        "imageName": uuidNew
    }
    result_info = jd_picture2good(
        scriptParams=scriptParams,
        subInteractiveName="search_goods_by_photo",
        req_id=req_id, uid=uid, scriptId=scriptId,conversation_id=conv_id)
    result_url = jd_picture2good(
        scriptParams={"index":1},
        subInteractiveName="search_goods_by_photo",
        req_id=req_id, uid=uid, scriptId=scriptId,conversation_id=conv_id)
    return parse_response(result_info),result_url

def jd_picture2good_new(
    imageUrl,
    req_id="717fb680-b3dc-42a8-a15a-fc8926984d61",
    uid="1956342083107160064",
    scriptId="1000012"
):
    uuidNew = str(uuid.uuid4()) +".jpg"# 生成一个随机的 UUID
    
    scriptParams = {
        "imageUrl": imageUrl,
        "imageName": uuidNew
    }
    result_info = jd_picture2good(
        scriptParams=scriptParams,
        subInteractiveName="search_goods_by_photo",
        req_id=req_id, uid=uid, scriptId=scriptId)
    # print(type(parse_response(result_info)))
    # print(parse_response(result_info))
    return parse_response(result_info)
    

# 示例调用（保留用于测试）
if __name__ == "__main__":
    # 示例参数
    scriptParams = {
        "imageUrl": "https://sl-bj-oss-bucket001.oss-cn-beijing.aliyuncs.com/test_photo/20250802/camera_video0_20250802_191718_0_right.jpg",
        "imageName": "jd-e295b8c3-4d7a-4f01-9b2a-7c1d4e8f6a9b.jpg"
    }
    
    # 调用函数
    result = jd_picture2good(
        scriptParams=scriptParams,
        subInteractiveName="search_goods_by_photo"
    )
    
    print("✅ 请求成功，返回内容：")
    print(json.dumps(result, indent=4, ensure_ascii=False))
